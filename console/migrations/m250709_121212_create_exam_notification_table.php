<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%exam_notification}}`.
 *
 * Has foreign keys to the tables:
 *
 * - `{{%exam}}`
 * - `{{%course}}`
 * - `{{%program}}`
 */
class m250709_121212_create_exam_notification_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $options = null;
        if (\Yii::$app->db->getDriverName() === 'mysql') {
            $options = 'CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB';
        }

        $this->createTable('{{%exam_notification}}', [
            'id' => $this->primaryKey(),
            'exam_id' => $this->integer()->defaultValue(null),
            'course_id' => $this->integer()->defaultValue(null),
            'program_id' => $this->integer()->defaultValue(null),
            'text' => $this->string()->defaultValue(null),
            'content' => $this->getDb()->getSchema()->createColumnSchemaBuilder('longtext'),
            'status' => $this->tinyInteger()->defaultValue(0),
            'created_at' => $this->dateTime(),
            'updated_at' => $this->dateTime(),
            'start_date' => $this->dateTime()->defaultValue(null),
            'end_date' => $this->dateTime()->defaultValue(null),
            'publish_at' => $this->dateTime()->defaultValue(null),
        ], $options);

        // add foreign key for table `{{%exam}}`
        $this->addForeignKey(
            '{{%fk-exam_notification-exam_id}}',
            '{{%exam_notification}}',
            'exam_id',
            '{{%exam}}',
            'id',
            'CASCADE'
        );

        // add foreign key for table `{{%course}}`
        $this->addForeignKey(
            '{{%fk-exam_notification-course_id}}',
            '{{%exam_notification}}',
            'course_id',
            '{{%course}}',
            'id',
            'CASCADE'
        );

        // add foreign key for table `{{%program}}`
        $this->addForeignKey(
            '{{%fk-exam_notification-program_id}}',
            '{{%exam_notification}}',
            'program_id',
            '{{%program}}',
            'id',
            'CASCADE'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropForeignKey(
            '{{%fk-exam_notification-exam_id}}',
            '{{%exam_notification}}'
        );

        $this->dropForeignKey(
            '{{%fk-exam_notification-course_id}}',
            '{{%exam_notification}}'
        );

        $this->dropForeignKey(
            '{{%fk-exam_notification-program_id}}',
            '{{%exam_notification}}'
        );

        $this->dropTable('{{%exam_notification}}');
    }
}
