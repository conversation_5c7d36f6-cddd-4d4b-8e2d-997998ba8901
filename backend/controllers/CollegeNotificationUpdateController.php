<?php

namespace backend\controllers;

use common\helpers\ContentHelper;
use common\models\College;
use Yii;
use common\models\CollegeNotificationUpdate;
use common\models\CollegeNotificationUpdateSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * CollegeNotificationUpdateController implements the CRUD actions for CollegeNotificationUpdate model.
 */
class CollegeNotificationUpdateController extends Controller
{
    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all CollegeNotificationUpdate models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new CollegeNotificationUpdateSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single CollegeNotificationUpdate model.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new CollegeNotificationUpdate model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new CollegeNotificationUpdate();
        $postData = Yii::$app->request->post();

        $uploadMode = Yii::$app->request->post('CollegeNotificationUpdate')['upload_type'] ?? 'single'; // Default to single upload

        if ($uploadMode === 'bulk' && isset($_FILES['bulk_csv']) && !empty($_FILES['bulk_csv']['tmp_name'])) {
            $tempModel = new CollegeNotificationUpdate();
            $tempModel->load($postData);
            return $this->processBulkUploadFromForm($tempModel, $_FILES['bulk_csv']);
        }

        // Check if bulk mode but no file
        if ($uploadMode === 'bulk') {
            Yii::$app->session->setFlash('error', 'Bulk upload mode selected but no CSV file uploaded.');
            return $this->render('create', ['model' => $model]);
        }

        // Single upload - normal validation
        $model->upload_type = $uploadMode;
        if ($model->load($postData)) {
            if ($model->status == 1) {
                $model->publish_at = new \yii\db\Expression('NOW()');
            }

            if ($model->save()) {
                return $this->redirect(['view', 'id' => $model->id]);
            }
        }

        return $this->render('create', [
            'model' => $model,
        ]);
    }

    /**
     * Process bulk upload from create form
     * @param CollegeNotificationUpdate $model
     * @param array $csvFile
     * @return mixed
     */
    private function processBulkUploadFromForm($model, $csvFile)
    {
        $filePath = $csvFile['tmp_name'];

        if (!file_exists($filePath)) {
            Yii::$app->session->setFlash('error', 'CSV file not found.');
            return $this->redirect('create');
        }

        if (!$this->isValidCsvMime($filePath)) {
            Yii::$app->session->setFlash('error', 'Invalid CSV File - Mime Type Mismatch');
            return $this->redirect('create');
        }

        $successCount = 0;
        $errorCount = 0;
        $failDetails = [];

        if (($handle = fopen($filePath, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $row = 0;
        while (($data = fgetcsv($handle, 2000, ',')) !== false) {
            if ($row == 0 || empty($data[0])) {
                $row++;
                continue;
            }

            $newModel = new CollegeNotificationUpdate();
            $collegeId = (int) ($data[0] ?? '');
            $slugFromCSV = trim($data[1] ?? '');

            $collegeModel = College::findOne($collegeId);
            if (!$collegeModel) {
                $failDetails[] = "Row {$row}: College with ID {$collegeId} not found.";
                $errorCount++;
                continue;
            }

            $validSubPages = array_map('strtolower', array_column($collegeModel->collegeContents, 'sub_page'));
            if (!in_array(strtolower($slugFromCSV), $validSubPages)) {
                $failDetails[] = "Row {$row}: Sub Page '{$slugFromCSV}' not found for College {$collegeId} || {$collegeModel->name}.";
                $errorCount++;
                continue;
            }

            $newModel->college_id = $collegeId;
            $newModel->sub_page = $slugFromCSV;
            $newModel->text = ContentHelper::removeStyleTag(stripslashes(html_entity_decode($model->text))) ?? '';
            $newModel->content = ContentHelper::removeStyleTag(stripslashes(html_entity_decode($model->content))) ?? '';
            $newModel->status = $model->status ?? CollegeNotificationUpdate::STATUS_INACTIVE;
            $newModel->start_date = $model->start_date ?? null;
            $newModel->end_date = $model->end_date ?? null;

            if ($newModel->status == CollegeNotificationUpdate::STATUS_ACTIVE) {
                $newModel->publish_at = new \yii\db\Expression('NOW()');
            }

            if ($newModel->save()) {
                $successCount++;
            } else {
                $errorCount++;
                $errors = array_map(
                    function ($attr, $messages) {
                        $label = ucfirst(str_replace('_', ' ', $attr));
                        return $label . ': ' . implode(', ', $messages);
                    },
                    array_keys($newModel->getErrors()),
                    $newModel->getErrors()
                );
                $failDetails[] = "Row {$row}: " . implode(', ', $errors);
            }
        }

        fclose($handle);

        // Final flash
        if ($errorCount > 0) {
            $list = '<ul><li>' . implode('</li><li>', $failDetails) . '</li></ul>';
            Yii::$app->session->setFlash('warning', "CSV Import was successful with {$successCount} records imported.<br>Failed Records: {$errorCount}<br>{$list}");
        } else {
            Yii::$app->session->setFlash('success', "CSV Import was successful. {$successCount} records imported.");
        }

        return $this->render('create', [
            'model' => $model,
        ]);
    }

    private function isValidCsvMime($filePath): bool
    {
        $allowedMimes = [
            'text/x-comma-separated-values',
            'text/comma-separated-values',
            'application/octet-stream',
            'application/vnd.ms-excel',
            'application/x-csv',
            'text/x-csv',
            'text/csv',
            'application/csv',
            'application/excel',
            'application/vnd.msexcel',
            'text/plain',
            'text/html',
        ];
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mime = finfo_file($finfo, $filePath);
        finfo_close($finfo);
        return in_array($mime, $allowedMimes, true);
    }

    /**
     * Updates an existing CollegeNotificationUpdate model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->id]);
        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }

    /**
     * Deletes an existing CollegeNotificationUpdate model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the CollegeNotificationUpdate model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return CollegeNotificationUpdate the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = CollegeNotificationUpdate::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }
}
