<?php

namespace backend\models;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use common\models\ExamNotification;

/**
 * ExamNotificationSearch represents the model behind the search form of `common\models\ExamNotification`.
 */
class ExamNotificationSearch extends ExamNotification
{
    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'status'], 'integer'],
            [['text', 'content', 'created_at', 'updated_at', 'start_date', 'end_date', 'publish_at', 'exam_id', 'course_id', 'program_id'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = ExamNotification::find();
        $query->joinWith(['exam', 'course', 'program']);

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => ['defaultOrder' => ['id' => SORT_DESC]]
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'id' => $this->id,
            // 'exam_id' => $this->exam_id,
            // 'course_id' => $this->course_id,
            // 'program_id' => $this->program_id,
            'status' => $this->status,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'start_date' => $this->start_date,
            'end_date' => $this->end_date,
            'publish_at' => $this->publish_at,
        ]);

        $query->andFilterWhere(['like', 'text', $this->text])
            ->andFilterWhere(['like', 'exam.name', $this->exam_id])
            ->andFilterWhere(['like', 'course.name', $this->course_id])
            ->andFilterWhere(['like', 'program.name', $this->program_id])
            ->andFilterWhere(['like', 'content', $this->content]);

        return $dataProvider;
    }
}
