<?php

use common\helpers\ContentHelper;
use common\helpers\DataHelper;
use common\models\College;
use common\models\Course;
use common\models\Exam;
use common\models\ExamNotification;
use common\models\Program;
use kartik\date\DatePicker;
use kartik\select2\Select2;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\web\JsExpression;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model common\models\ExamNotification */
/* @var $form yii\widgets\ActiveForm */

$data = ArrayHelper::map(Exam::find()->where(['id' => $model->exam_id])->all(), 'id', 'name');
$programData = ArrayHelper::map(Program::find()->where(['id' => $model->program_id])->all(), 'id', 'name');
$courseData = ArrayHelper::map(Course::find()->where(['id' => $model->course_id])->all(), 'id', 'name');
$content = ContentHelper::removeStyleTag(stripslashes(html_entity_decode($model->content)));
?>

<div class="exam-notification-form box box-primary">
    <?php $form = ActiveForm::begin(); ?>
    <div class="box-body table-responsive">

        <?=
        $form->field($model, 'exam_id')->widget(Select2::class, [
            'disabled' => !$model->isNewRecord,
            'data' => $data, // array of text to show in the tag for the selected items
            'options' => [
                'placeholder' => '--Select Exams (Max 2)--',
                'multiple' => true,
            ],
            'pluginOptions' => [
                'allowClear' => true,
                'minimumInputLength' => 1,
                'maximumSelectionLength' => 2,
                'language' => [
                    'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                    'maximumSelected' => new JsExpression("function () { return 'You can only select maximum 2 exams'; }"),
                ],
                'ajax' => [
                    'url' => ['../ajax/exam-list'],
                    'dataType' => 'json',
                    'data' => new JsExpression('function(params) {return {query:params.term}; }')
                ],
                'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                'templateResult' => new JsExpression('function(data) { return data.text; }'),
                'templateSelection' => new JsExpression('function (data) { return data.text; }'),
            ],
        ])->label('Exam Name (Max 2)');
        ?>

        <?=
        $form->field($model, 'course_id')->widget(Select2::class, [
            'disabled' => !$model->isNewRecord,
            'data' => $courseData, // array of text to show in the tag for the selected items
            'options' => [
                'placeholder' => '--Select Course--',
                'multiple' => true,
            ],
            'pluginOptions' => [
                'allowClear' => true,
                'language' => [
                    'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                ],
                'ajax' => [
                    'url' => ['../ajax/exam-course'],
                    'dataType' => 'json',
                    'data' => new JsExpression('function(params) {return {exam_id:$("#examnotification-exam_id").val(), q:params.term}; }')
                ],
                'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                'templateResult' => new JsExpression('function(data) { return data.text; }'),
                'templateSelection' => new JsExpression('function (data) { return data.text; }'),
            ],
        ])->label('Course');
        ?>

        <?=
        $form->field($model, 'program_id')->widget(Select2::class, [
            'disabled' => !$model->isNewRecord,
            'data' => $programData, // array of text to show in the tag for the selected items
            'options' => [
                'placeholder' => '--Select Program--',
                'multiple' => true,
            ],
            'pluginOptions' => [
                'allowClear' => true,
                'language' => [
                    'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                ],
                'ajax' => [
                    'url' => ['../ajax/program-course'],
                    'dataType' => 'json',
                    'data' => new JsExpression('function(params) {return {course_id:$("#examnotification-course_id").val(), q:params.term}; }')
                ],
                'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                'templateResult' => new JsExpression('function(data) { return data.text; }'),
                'templateSelection' => new JsExpression('function (data) { return data.text; }'),
            ],
        ])->label('Program');
        ?>

        <div class="alert alert-info">
            <strong>Note:</strong> You can select up to 2 exams. Course and Program selection is optional. The system will:
            <ul>
                <li><strong>Multiple exams only:</strong> Create notifications for each exam with null course_id and program_id</li>
                <li><strong>Exams + Courses:</strong> Create notifications only for valid exam-course combinations (based on exam-course mapping)</li>
                <li><strong>Exams + Courses + Programs:</strong> Create notifications for valid exam-course-program combinations</li>
                <li><strong>Invalid combinations:</strong> If no valid exam-course combinations exist, store exam with null course/program</li>
            </ul>
        </div>

        <?=
        $this->render('/widget/tinymce', [
            'form' => $form,
            'model' => $model,
            'type' => College::ENTITY_COLLEGE,
            'entity' => 'content'
        ])
        ?>
        <?= $form->field($model, 'text')->textInput()->label('Heading Text') ?>

        <?=
        $form->field($model, 'start_date')->widget(DatePicker::class, [
            'value' => '',
            'options' => ['placeholder' => 'Select date ...', 'class' => 'start_date'],
            'type' => DatePicker::TYPE_COMPONENT_APPEND,
            'pluginOptions' => [
                'format' => 'yyyy-mm-dd',
                'minViewMode' => 'month',
                'startDate' => date('Y-m-d'),
                'todayHighlight' => true,
                'autocomplete' => 'off',
            ],
        ])->label('Start Date');
        ?>
        <?=
        $form->field($model, 'end_date')->widget(DatePicker::class, [
            'value' => '',
            'options' => ['placeholder' => 'Select date ...', 'class' => 'start_date'],
            'type' => DatePicker::TYPE_COMPONENT_APPEND,
            'pluginOptions' => [
                'format' => 'yyyy-mm-dd',
                'minViewMode' => 'month',
                'startDate' => date('Y-m-d'),
                'todayHighlight' => true,
                'autocomplete' => 'off',
            ],
        ])->label('End Date');
        ?>

        <?= $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', ExamNotification::class)) ?>

    </div>
    <div class="box-footer">
        <?= Html::submitButton('Save', ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <?php ActiveForm::end(); ?>
</div>